package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.FundingModel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.BankChannelType;
import com.jinghang.capital.core.enums.FlowChannel;
import com.jinghang.capital.core.enums.LoanStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 借款表
 */
@Entity
@Table(name = "loan")
public class Loan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 2102947086790163069L;

    /**
     * 用户id
     */
    private String accountId;
    /**
     * 授信id
     */
    private String creditId;
    /**
     * 外部id
     */
    private String outerLoanId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;
    /**
     * 产品编号
     */
    private String productNo;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 金额
     */
    private BigDecimal loanAmt;
    /**
     * 总期数
     */
    private Integer periods;
    /**
     * 借款用途
     */
    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;
    /**
     * 借据状态
     */
    @Enumerated(EnumType.STRING)
    private LoanStatus loanStatus;


    /**
     * 辅助模式
     */
    private String assistMode;


    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 放款时间
     */
    private LocalDateTime loanTime;

    /**
     * 借据编号
     */
    private String loanNo;

    /**
     * 资产端放款申请合同编号
     */
    private String loanApplyContractNo;

    /**
     * 委托担保合同编号
     */
    private String guaranteeContractNo;

    private String loanContractNo;

    /**
     * 放款资方
     */
    private String loanChannel;
    /**
     * 放款卡id
     */
    private String loanCardId;
    /**
     * 还款卡id
     */
    private String repayCardId;
    /**
     * 用户姓名
     */
    private String custName;
    /**
     * 用户手机
     */
    private String custMobile;
    /**
     * 用户身份证
     */
    private String custCertNo;
    /**
     * 对客费率
     */
    private BigDecimal customRate;
    /**
     * 对资费率
     */
    private BigDecimal bankRate;
    /**
     * 富民渠道类型 fbankV2 fbankV3
     */
    @Enumerated(EnumType.STRING)
    private BankChannelType channelType;

    /**
     * 出资模式
     */
    @Enumerated(EnumType.STRING)
    private FundingModel fundingModel;

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    /**
     * 用户id
     */
    public String getAccountId() {
        return this.accountId;
    }

    /**
     * 用户id
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 授信id
     */
    public String getCreditId() {
        return this.creditId;
    }

    /**
     * 授信id
     */
    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    /**
     * 外部id
     */
    public String getOuterLoanId() {
        return this.outerLoanId;
    }

    /**
     * 外部id
     */
    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    /**
     * 资方渠道
     */
    public BankChannel getChannel() {
        return this.channel;
    }

    /**
     * 资方渠道
     */
    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getAssistMode() {
        return assistMode;
    }

    public void setAssistMode(String assistMode) {
        this.assistMode = assistMode;
    }

    /**
     * 产品编号
     */
    public String getProductNo() {
        return this.productNo;
    }

    /**
     * 产品编号
     */
    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getLoanApplyContractNo() {
        return loanApplyContractNo;
    }

    public void setLoanApplyContractNo(String loanApplyContractNo) {
        this.loanApplyContractNo = loanApplyContractNo;
    }


    public String getGuaranteeContractNo() {
        return guaranteeContractNo;
    }

    public void setGuaranteeContractNo(String guaranteeContractNo) {
        this.guaranteeContractNo = guaranteeContractNo;
    }

    public String getLoanChannel() {
        return loanChannel;
    }

    public void setLoanChannel(String loanChannel) {
        this.loanChannel = loanChannel;
    }

    /**
     * 总期数
     */
    public Integer getPeriods() {
        return this.periods;
    }

    /**
     * 总期数
     */
    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    /**
     * 借据状态
     */
    public LoanStatus getLoanStatus() {
        return this.loanStatus;
    }

    /**
     * 借据状态
     */
    public void setLoanStatus(LoanStatus loanStatus) {
        this.loanStatus = loanStatus;
    }

    /**
     * 放款时间
     */
    public LocalDateTime getLoanTime() {
        return this.loanTime;
    }

    /**
     * 放款时间
     */
    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanContractNo() {
        return loanContractNo;
    }

    public void setLoanContractNo(String loanContractNo) {
        this.loanContractNo = loanContractNo;
    }

    /**
     * 放款卡id
     */
    public String getLoanCardId() {
        return this.loanCardId;
    }

    /**
     * 放款卡id
     */
    public void setLoanCardId(String loanCardId) {
        this.loanCardId = loanCardId;
    }

    /**
     * 还款卡id
     */
    public String getRepayCardId() {
        return this.repayCardId;
    }

    /**
     * 还款卡id
     */
    public void setRepayCardId(String repayCardId) {
        this.repayCardId = repayCardId;
    }

    /**
     * 用户姓名
     */
    public String getCustName() {
        return this.custName;
    }

    /**
     * 用户姓名
     */
    public void setCustName(String custName) {
        this.custName = custName;
    }

    /**
     * 用户手机
     */
    public String getCustMobile() {
        return this.custMobile;
    }

    /**
     * 用户手机
     */
    public void setCustMobile(String custMobile) {
        this.custMobile = custMobile;
    }

    /**
     * 用户身份证
     */
    public String getCustCertNo() {
        return this.custCertNo;
    }

    /**
     * 用户身份证
     */
    public void setCustCertNo(String custCertNo) {
        this.custCertNo = custCertNo;
    }

    /**
     * 对客费率
     */
    public BigDecimal getCustomRate() {
        return this.customRate;
    }

    /**
     * 对客费率
     */
    public void setCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
    }

    /**
     * 对资费率
     */
    public BigDecimal getBankRate() {
        return this.bankRate;
    }

    /**
     * 对资费率
     */
    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BankChannelType getChannelType() {
        return channelType;
    }

    public void setChannelType(BankChannelType channelType) {
        this.channelType = channelType;
    }


    public FundingModel getFundingModel() {
        return fundingModel;
    }

    public void setFundingModel(FundingModel fundingModel) {
        this.fundingModel = fundingModel;
    }

    @Override
    public String prefix() {
        return "LO";
    }


}
