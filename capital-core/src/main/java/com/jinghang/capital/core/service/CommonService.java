package com.jinghang.capital.core.service;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.convert.entity.AgreementConvert;
import com.jinghang.capital.core.convert.entity.CreditLoanConvert;
import com.jinghang.capital.core.convert.entity.ProtocolSyncConvert;
import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.AccountContactInfo;
import com.jinghang.capital.core.entity.AccountDevice;
import com.jinghang.capital.core.entity.AgreementSignature;
import com.jinghang.capital.core.entity.BankBatchSubstituteRecord;
import com.jinghang.capital.core.entity.BankConfig;
import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.BatchCustomerRepayDetail;
import com.jinghang.capital.core.entity.BatchCustomerRepayRecord;
import com.jinghang.capital.core.entity.ClaimAfterRepayRecord;
import com.jinghang.capital.core.entity.CovenantDownloadApplyRecord;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.CustomerLoanReplan;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.DefrayRecord;
import com.jinghang.capital.core.entity.FailedAccount;
import com.jinghang.capital.core.entity.FailedAccountBankCard;
import com.jinghang.capital.core.entity.FailedAccountContactInfo;
import com.jinghang.capital.core.entity.FailedAccountDevice;
import com.jinghang.capital.core.entity.FailedCredit;
import com.jinghang.capital.core.entity.FailedLoanFile;
import com.jinghang.capital.core.entity.FlowClaimRecord;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.entity.QuotaAdjustRecord;
import com.jinghang.capital.core.entity.ReconciliationFile;
import com.jinghang.capital.core.entity.Shop;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.Gender;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.PushStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.enums.SignatureType;
import com.jinghang.capital.core.enums.WhetherState;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.AccountContactInfoRepository;
import com.jinghang.capital.core.repository.AccountDeviceRepository;
import com.jinghang.capital.core.repository.AccountRepository;
import com.jinghang.capital.core.repository.AgreementSignatureRepository;
import com.jinghang.capital.core.repository.BankBatchSubstituteRecordRepository;
import com.jinghang.capital.core.repository.BankConfigRepository;
import com.jinghang.capital.core.repository.BankLoanReplanRepository;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.BatchCustomerRepayDetailRepository;
import com.jinghang.capital.core.repository.BatchCustomerRepayRecordRepository;
import com.jinghang.capital.core.repository.ClaimAfterRepayRecordRepository;
import com.jinghang.capital.core.repository.ClaimRecordRepository;
import com.jinghang.capital.core.repository.CovenantDownloadApplyRecordRepository;
import com.jinghang.capital.core.repository.CreditExtRepository;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.CustomerLoanReplanRepository;
import com.jinghang.capital.core.repository.CustomerRepayRecordRepository;
import com.jinghang.capital.core.repository.DefrayRecordRepository;
import com.jinghang.capital.core.repository.FailedAccountBankCardRepository;
import com.jinghang.capital.core.repository.FailedAccountContactInfoRepository;
import com.jinghang.capital.core.repository.FailedAccountDeviceRepository;
import com.jinghang.capital.core.repository.FailedAccountRepository;
import com.jinghang.capital.core.repository.FailedCreditRepository;
import com.jinghang.capital.core.repository.FailedLoanFileRepository;
import com.jinghang.capital.core.repository.HXBKAccountAttrRepository;
import com.jinghang.capital.core.repository.HXBKAntFileLogRepository;
import com.jinghang.capital.core.repository.HXBKCreditFlowRepository;
import com.jinghang.capital.core.repository.LoanFileRepository;
import com.jinghang.capital.core.repository.LoanReplanRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.repository.QuotaAdjustRecordRepository;
import com.jinghang.capital.core.repository.ReconciliationFileRepository;
import com.jinghang.capital.core.repository.ShopRepository;
import com.jinghang.capital.core.vo.FileInfoVo;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.credit.BankCardInfoVo;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.ExtInfoVo;
import com.jinghang.capital.core.vo.credit.IdCardInfoVo;
import com.jinghang.capital.core.vo.credit.MerchantInfoVo;
import com.jinghang.capital.core.vo.credit.UserContactInfoVo;
import com.jinghang.capital.core.vo.credit.UserInfoVo;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncVo;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


@Service
public class CommonService {

    private static final Logger logger = LoggerFactory.getLogger(CommonService.class);

    public static final int BIRTH_POS_START = 6;
    public static final int BIRTH_POS_END = 14;
    public static final int GENDER_POS_START = 16;
    public static final int GENDER_POS_END = 17;


    public static final String[] PRODUCT_TYPE_ARR = {"21201DFYLS2", "21202DFYLS2"};
    public static final int PRODUCT_TYPE_RANDMO = 10;
    public static final int PRODUCT_TYPE_SPLIT_NUM = 3;

    /**
     * 是否允许重推放款
     * <p>
     * 此开关默认关闭
     * 在资方批量放款异常时可以打开,业务系统会用相同的外部借据号请求
     */
    @Value("${allow.repushLoan:false}")
    private Boolean allowRepushLoan;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private CreditExtRepository creditExtRepository;

    @Autowired
    private LoanFileRepository loanFileRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private AccountContactInfoRepository accountContactInfoRepository;

    @Autowired
    private AccountBankCardRepository accountBankCardRepository;

    @Autowired
    private AccountDeviceRepository accountDeviceRepository;

    @Autowired
    private AgreementSignatureRepository agreementSignatureRepository;

    @Autowired
    private FailedAccountRepository failedAccountRepository;

    @Autowired
    private FailedAccountContactInfoRepository failedAccountContactInfoRepository;

    @Autowired
    private FailedAccountBankCardRepository failedAccountBankCardRepository;

    @Autowired
    private FailedAccountDeviceRepository failedAccountDeviceRepository;

    @Autowired
    private FailedCreditRepository failedCreditRepository;

    @Autowired
    private FailedLoanFileRepository failedLoanFileRepository;

    @Autowired
    private ShopRepository shopRepository;

    @Autowired
    private ReconciliationFileRepository recFileRepository;

    @Autowired
    private CustomerRepayRecordRepository customerRepayRecordRepository;

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    @Autowired
    private BankLoanReplanRepository bankLoanReplanRepository;


    @Autowired
    private ClaimAfterRepayRecordRepository claimAfterRepayRecordRepository;


    @Autowired
    private LoanReplanRepository loanReplanRepository;

    @Autowired
    private BatchCustomerRepayRecordRepository batchCustomerRepayRecordRepository;

    @Autowired
    private BatchCustomerRepayDetailRepository batchCustomerRepayDetailRepository;


    @Autowired
    private MqService mqService;

    @Autowired
    private DefrayRecordRepository defrayRecordRepository;

    @Autowired
    private CovenantDownloadApplyRecordRepository covenantDownloadApplyRecordRepository;

    @Autowired
    private QuotaAdjustRecordRepository quotaAdjustRecordRepository;

    @Autowired
    private BankConfigRepository bankConfigRepository;

    @Autowired
    private BankBatchSubstituteRecordRepository bankBatchSubstituteRecordRepository;

    @Autowired
    private CustomerLoanReplanRepository customerLoanReplanRepository;


    @Autowired
    private ClaimRecordRepository claimRecordRepository;

    @Autowired
    private HXBKCreditFlowRepository hxbkCreditFlowRepository;

    @Autowired
    private HXBKAccountAttrRepository hxbkAccountAttrRepository;

    @Autowired
    private HXBKAntFileLogRepository hxbkAntFileLogRepository;

    @Autowired
    public void setRecFileRepository(ReconciliationFileRepository recFileRepository) {
        this.recFileRepository = recFileRepository;
    }

    @Autowired
    public void setCustomerRepayRecordRepository(CustomerRepayRecordRepository customerRepayRecordRepository) {
        this.customerRepayRecordRepository = customerRepayRecordRepository;
    }

    public void setBankRepayRecordRepository(BankRepayRecordRepository bankRepayRecordRepository) {
        this.bankRepayRecordRepository = bankRepayRecordRepository;
    }

    public ReconciliationFile findRecFileById(String recFileId) {
        return recFileRepository.findById(recFileId).orElseThrow(() -> new BizException(BizErrorCode.REC_NOT_FOUND));
    }

    public ReconciliationFile updateRecFile(ReconciliationFile recFile) {
        return recFileRepository.save(recFile);
    }

    public Credit findCreditById(String creditId) {
        return creditRepository.findById(creditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND));
    }

    public FailedCredit findFailedCreditById(String failedCreditId) {
        return failedCreditRepository.findById(failedCreditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND));
    }

    public Credit findCreditByOutId(String outerCreditId) {
        return creditRepository.findCreditByOuterCreditId(outerCreditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND));
    }

    public Account findAccountById(String accountId) {
        return accountRepository.findById(accountId).orElseThrow(() -> new BizException(BizErrorCode.ACCOUNT_NOT_FOUND));
    }

    public List<Credit> findSuccessCredit(BankChannel channel, LocalDate creditTime) {
        LocalDateTime startOfDay = creditTime.atStartOfDay();
        LocalDateTime nextDayStart = creditTime.plusDays(1L).atStartOfDay();
        return creditRepository.findCreditListByApplyTimeAndStatus(channel, CreditStatus.SUCCESS, startOfDay, nextDayStart);
    }

    public Credit getLastValidCredit(String idNo, BankChannel channel) {
        return creditRepository.getValidCredit(idNo, CreditStatus.SUCCESS.name(), channel.name()).orElseThrow();
    }

    public Loan findLoanById(String loanId) {
        return loanRepository.findById(loanId).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
    }

    public Loan findLoanByCreditId(String loanId) {
        return loanRepository.findByCreditId(loanId).orElse(null);
    }


    public Loan findLoanByOutId(String outerLoanId) {
        return loanRepository.findByOuterLoanId(outerLoanId).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
    }

    public Loan findLoanByLoanNo(String loanNo) {
        return loanRepository.findByLoanNo(loanNo).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
    }

    public List<Loan> findSuccessLoan(BankChannel channel, LocalDate loanTime) {
        LocalDateTime startOfDay = loanTime.atStartOfDay();
        LocalDateTime nextDayStart = loanTime.plusDays(1L).atStartOfDay();
        return loanRepository.findByChannelAndLoanStatusAndLoanTime(channel, LoanStatus.SUCCESS, startOfDay, nextDayStart);
    }

    public List<String> findSuccessLoanIds(BankChannel channel, LocalDate loanTime) {
        LocalDateTime startOfDay = loanTime.atStartOfDay();
        LocalDateTime nextDayStart = loanTime.plusDays(1L).atStartOfDay();

        return loanRepository.findIdsByChannelAndLoanStatusAndLoanTime(channel, LoanStatus.SUCCESS, startOfDay, nextDayStart);
    }


    public List<Loan> findByLoanIds(List<String> ids) {
        return loanRepository.findByIdIn(ids);
    }

    public long findByLoanAndIdsAndLoanTime(List<String> loanIds, LocalDate localDate) {
        LocalDateTime startOfDay = localDate.atStartOfDay();
        LocalDateTime nextDayStart = localDate.plusDays(1L).atStartOfDay();
        return loanRepository.countByIdInAndLoanStatusAndLoanTimeIsGreaterThanEqualAndLoanTimeIsLessThan(loanIds, LoanStatus.SUCCESS, startOfDay, nextDayStart);
    }

    public List<BankRepayRecord> findSuccessBankRepayRecord(BankChannel channel, LocalDate loanTime) {
        LocalDateTime startOfDay = loanTime.atStartOfDay();
        LocalDateTime nextDayStart = loanTime.plusDays(1L).atStartOfDay();
        return bankRepayRecordRepository.findByChannelAndLoanStatusAndLoanTime(channel, ProcessStatus.SUCCESS, startOfDay, nextDayStart);
    }

    public BankRepayRecord findMaxPeriodBankRepayRecordByLoanId(String loanId) {
        return bankRepayRecordRepository.findFirstByLoanIdAndRepayStatusOrderByPeriodDesc(loanId, ProcessStatus.SUCCESS);
    }

    public List<BankRepayRecord> findBankRepayRecordByLoanId(String loanId) {
        return bankRepayRecordRepository.findByLoanId(loanId);
    }

    public CustomerLoanReplan saveCustomerLoanReplan(CustomerLoanReplan customerLoanReplan) {
        return customerLoanReplanRepository.save(customerLoanReplan);
    }

    public CustomerRepayRecord saveRepayRecord(CustomerRepayRecord customerRepayRecord) {
        return customerRepayRecordRepository.save(customerRepayRecord);
    }

    public CustomerRepayRecord findRepayRecordById(String repayId) {
        return customerRepayRecordRepository.findById(repayId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }


    public CustomerRepayRecord findRepayRecordByOutId(String repayOuterId) {
        return customerRepayRecordRepository.findByOuterRepayId(repayOuterId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public List<CustomerRepayRecord> findListByOuterRepayIdOrderByPeriodAsc(String repayOuterId) {
        return customerRepayRecordRepository.findListByOuterRepayIdOrderByPeriodAsc(repayOuterId);
    }

    public Optional<CustomerRepayRecord> findPossiblyRepayRecordByOutId(String repayOuterId) {
        return customerRepayRecordRepository.findByOuterRepayId(repayOuterId);
    }

    public Optional<CustomerRepayRecord> findSuccessRepayRecordByLoanIdAndPeriod(String loanId, Integer period) {
        return customerRepayRecordRepository.findByLoanIdAndPeriodAndRepayStatus(loanId, period, ProcessStatus.SUCCESS);
    }

    public Optional<CustomerRepayRecord> findRepayRecordByLoanIdAndPeriodAndStatus(String loanId, Integer period) {
        return customerRepayRecordRepository.findByLoanIdAndPeriodAndRepayStatusIn(loanId,
                period, List.of(ProcessStatus.PROCESSING, ProcessStatus.INIT, ProcessStatus.SUCCESS));
    }


    public List<CustomerRepayRecord> findListSuccessRepayRecordByLoanIdAndPeriod(String loanId, Integer period) {
        return customerRepayRecordRepository.findListByLoanIdAndPeriodAndRepayStatus(loanId, period, ProcessStatus.SUCCESS);
    }

    public List<CustomerRepayRecord> findListSuccessRepayRecordByChannelAndRepayTimeAndRepayTypeAndRepayMode(BankChannel channel, LocalDate repayTime,
                                                                                                             RepayType repayType, RepayMode repayMode) {
        LocalDateTime startOfDay = repayTime.atStartOfDay();
        LocalDateTime nextDayStart = repayTime.plusDays(1L).atStartOfDay();
        return customerRepayRecordRepository.findListByChannelAndRepayTypeAndRepayModeAndRepayStatusAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(
                channel, repayType, repayMode, ProcessStatus.SUCCESS, startOfDay, nextDayStart);
    }


    public List<CustomerRepayRecord> findCustomerRepayRecordsByIds(List<String> relatedIds) {
        return customerRepayRecordRepository.findByIdIn(relatedIds);
    }

    public BankRepayRecord findBankRepayRecord(String repayId) {
        return bankRepayRecordRepository.findById(repayId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public List<BankRepayRecord> findListSuccessBankRepayRecordByLoanIdAndPeriod(String loanId, Integer period) {
        return bankRepayRecordRepository.findListByLoanIdAndPeriodAndRepayStatus(loanId, period, ProcessStatus.SUCCESS);
    }

    public List<BankRepayRecord> findBankRepayRecordsByLoanIdAndPeriod(String loanId, Integer period) {
        return bankRepayRecordRepository.findBankRepayRecordsByLoanIdAndPeriod(loanId, period);
    }

    public BankLoanReplan findBankLoanReplan(String bankLoanReplanId) {
        return bankLoanReplanRepository.findById(bankLoanReplanId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }


    public List<BankLoanReplan> findBankLoanReplanByChannelAndRepayStatusAndRepayTypeAndActRepayTime(BankChannel bankChannel, RepayStatus repayStatus,
                                                                                                     RepayType repayType, LocalDate repayTime) {
        LocalDateTime startOfDay = repayTime.atStartOfDay();
        LocalDateTime nextDayStart = repayTime.plusDays(1L).atStartOfDay();
        return bankLoanReplanRepository.findListByChannelAndRepayStatusAndRepayTypeAndActRepayTimeIsGreaterThanEqualAndActRepayTimeIsLessThan(bankChannel,
                repayStatus, repayType, startOfDay, nextDayStart);
    }


    public List<BankLoanReplan> findBankLoanRepayPlansByIds(List<String> relatedIds) {
        return bankLoanReplanRepository.findByIdIn(relatedIds);
    }

    public ClaimAfterRepayRecord findClaimAfterRecord(String recordId) {
        return claimAfterRepayRecordRepository.findById(recordId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public ClaimAfterRepayRecord findSubstituteSuccessClaimAfterRecord(String loanId, Integer period) {
        return claimAfterRepayRecordRepository.findByLoanIdAndPeriodAndAndNotifyStatus(loanId, period, ProcessStatus.SUCCESS);
    }


    public AccountBankCard syncProtocol(Credit credit, ProtocolSyncVo syncVo) {
        AccountBankCard accountBankCard = accountBankCardRepository.save(ProtocolSyncConvert.INSTANCE.toEntity(syncVo));
        credit.setCardId(accountBankCard.getId());
        creditRepository.save(credit);

        return accountBankCard;
    }


    public AccountBankCard getBankCard(String cardId) {
        return accountBankCardRepository.findById(cardId).orElseThrow(() -> new BizException(BizErrorCode.BANK_PROTOCOL_NOT_FOUND));
    }

    public LoanReplan findByLoanIdAndPeriod(String loanId, Integer period) {
        return loanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public LoanReplan saveLoanReplan(LoanReplan loanReplan) {
        return loanReplanRepository.save(loanReplan);
    }

    public BatchCustomerRepayRecord saveBatchRepayRecord(BatchCustomerRepayRecord batchCustomerRepayRecord) {
        return batchCustomerRepayRecordRepository.save(batchCustomerRepayRecord);
    }

    public BatchCustomerRepayRecord findBatchRepayRecordById(String id) {
        return batchCustomerRepayRecordRepository.findById(id).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public BatchCustomerRepayRecord findBatchRepayRecordByOuterRepayId(String id) {
        return batchCustomerRepayRecordRepository.findByOuterRepayId(id).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public void saveAllRepayDetails(List<BatchCustomerRepayDetail> repayDetails) {
        batchCustomerRepayDetailRepository.saveAll(repayDetails);
    }


    public List<BatchCustomerRepayDetail> findBatchRepayDetailByBatchRepayId(String id) {
        return batchCustomerRepayDetailRepository.findByBatchRepayId(id);
    }


    public List<BatchCustomerRepayDetail> findByLoanNoAndPeriodAndStatus(String id, Integer period, ProcessStatus processStatus) {
        return batchCustomerRepayDetailRepository.findByLoanNoAndPeriodAndStatus(id, period, processStatus);
    }

    public FlowClaimRecord findClaimRecordByOuterClaimId(String outerClaimId) {
        return claimRecordRepository.findByOuterClaimId(outerClaimId);
    }

    public FlowClaimRecord findClaimRecordById(String outerClaimId) {
        return claimRecordRepository.findById(outerClaimId).orElse(null);
    }

    public Optional<CovenantDownloadApplyRecord> findCovenantDownloadApplyRecordById(String applyId) {
        return covenantDownloadApplyRecordRepository.findById(applyId);
    }

    public Optional<QuotaAdjustRecord> findQuotaAdjustRecordById(String adjustId) {
        return quotaAdjustRecordRepository.findById(adjustId);
    }

    public Optional<BankConfig> findBankConfigByChannelAndGuaranteeCompanyAndEnabled(BankChannel channel,
                                                                                     GuaranteeCompany guaranteeCompany,
                                                                                     WhetherState enabled) {
        return bankConfigRepository.findByChannelAndGuaranteeCompanyAndEnabled(channel, guaranteeCompany, enabled);
    }

    public Optional<BankBatchSubstituteRecord> findBankSubstituteRecordByLoanIdAndPeriod(String loanId, Integer period) {
        return bankBatchSubstituteRecordRepository.findByLoanIdAndPeriod(loanId, period);
    }

    public Optional<BankBatchSubstituteRecord> findBankSubstituteRecord(String loanId, Integer period) {
        return bankBatchSubstituteRecordRepository.findByLoanIdAndPeriod(loanId, period);
    }

    public BankBatchSubstituteRecord saveBankSubstituteRecord(BankBatchSubstituteRecord record) {
        return bankBatchSubstituteRecordRepository.save(record);
    }

    public List<BankBatchSubstituteRecord> findWaitProcessSubstituteRecords(BankChannel channel,
                                                                            GuaranteeCompany guaranteeCompany,
                                                                            LocalDate pushDate,
                                                                            PushStatus pushStatus) {
        return bankBatchSubstituteRecordRepository.findByChannelAndGuaranteeCompanyAndPushDateAndPushStatus(channel, guaranteeCompany, pushDate, pushStatus);
    }

    public BankBatchSubstituteRecord findBySubstituteId(String substituteId) {
        return bankBatchSubstituteRecordRepository.findById(substituteId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    /**
     * 更新
     *
     * @param credit
     * @return
     */
    public Credit saveCredit(Credit credit) {
        return creditRepository.save(credit);
    }


    /**
     * 更新借款
     *
     * @param loan
     * @return
     */
    public Loan saveLoan(Loan loan) {
        return loanRepository.save(loan);
    }

    /**
     * 更新
     *
     * @param credit
     * @return
     */
    public FailedCredit saveFailedCredit(FailedCredit credit) {
        return failedCreditRepository.save(credit);
    }

    /**
     * 可用授信额度
     *
     * @param credit
     */
    public void initCreditAvailable(Credit credit) {

        creditExtRepository.findById(credit.getId()).ifPresent(ext -> {

            // 可用授信额度
            ext.setAvailableCreditAmt(credit.getCreditResultAmt());
            ext.setOnLoanAmt(BigDecimal.ZERO);

            creditExtRepository.save(ext);
        });

    }

    /**
     * 改变在贷余额
     *
     * @param loanId
     * @param stage
     * @param amount
     * @return
     */
    public void changeOnLoan(String loanId, LoanStage stage, BigDecimal amount) {

        if (LoanStage.CREDIT.equals(stage)) {
            throw new RuntimeException("授信阶段不可改变在贷余额");
        }

        Loan loan = loanRepository.findById(loanId).orElseThrow();
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();

        // 更新在贷余额
        creditExtRepository.findById(credit.getId()).ifPresent(ext -> {
            if (LoanStage.LOAN.equals(stage)) {
                // 增加在贷, 授信可用余额减少
                ext.setOnLoanAmt(ext.getOnLoanAmt().add(amount));
                ext.setAvailableCreditAmt(ext.getAvailableCreditAmt().subtract(amount));
            }
            if (LoanStage.AFTER_LOAN.equals(stage)) {
                // 减少在贷, 授信可用余额增加
                ext.setOnLoanAmt(ext.getOnLoanAmt().subtract(amount));
                ext.setAvailableCreditAmt(ext.getAvailableCreditAmt().add(amount));
            }

            creditExtRepository.save(ext);
        });

    }


    /**
     * 放款请求落库
     *
     * @param applyVo 借款请求
     * @return 借款记录
     */
    public Loan loanApply(LoanApplyVo applyVo) {
        String creditId = applyVo.getCreditId();
        Credit credit = StringUtil.isNotBlank(creditId) ? findCreditById(creditId) : findCreditByOutId(applyVo.getSysCreditId());

        if (!allowRepushLoan && loanRepository.findByOuterLoanId(applyVo.getSysId()).isPresent()) {
            throw new BizException(BizErrorCode.LOAN_HAS_EXIST);
        }

        // 放款参数
        Loan loan = CreditLoanConvert.INSTANCE.creditToLoan(credit);
        loan.setOuterLoanId(applyVo.getSysId());
        loan.setLoanStatus(LoanStatus.INIT);
        loan.setLoanPurpose(applyVo.getLoanPurpose());
        loan.setGuaranteeContractNo(applyVo.getGuaranteeContractNo());

        if (applyVo.getGuaranteeCompany() != null) {
            loan.setGuaranteeCompany(applyVo.getGuaranteeCompany());
        }

        if (Objects.nonNull(applyVo.getLoanAmt())) {
            loan.setLoanAmt(applyVo.getLoanAmt());
        }
        if (Objects.nonNull(applyVo.getPeriods())) {
            loan.setPeriods(applyVo.getPeriods());
        }
        if (Objects.nonNull(applyVo.getCustomRate())) {
            loan.setCustomRate(applyVo.getCustomRate());
        }
        String loanApplyContractNo = applyVo.getLoanApplyContractNo();
        if (!StringUtil.isEmpty(loanApplyContractNo)) {
            loan.setLoanApplyContractNo(loanApplyContractNo);
        }

        loan.setLoanTime(LocalDateTime.now());

        // 有绑卡id时填充
        if (!StringUtil.isEmpty(applyVo.getCardId())) {
            loan.setRepayCardId(applyVo.getCardId());
            loan.setLoanCardId(applyVo.getCardId());
        } else {
            // bank_card
            BankCardInfoVo cardInfoVo = applyVo.getBankCardInfo();
            if (cardInfoVo != null && !StringUtil.isEmpty(cardInfoVo.getCardNo())) {
                AccountBankCard bankCard = CreditLoanConvert.INSTANCE.toLoanBankCard(cardInfoVo, loan);
                bankCard.setChannel(loan.getChannel());
                accountBankCardRepository.save(bankCard);
                String cardId = bankCard.getId();
                loan.setRepayCardId(cardId);
                loan.setLoanCardId(cardId);
            }
        }
        loan.setGuaranteeContractNo(applyVo.getGuaranteeContractNo());


        // 保存放款订单
        loan = loanRepository.save(loan);
        // 还款协议
        String repayAgreementNo = applyVo.getRepayAgreementNo();
        if (!StringUtil.isEmpty(repayAgreementNo)) {
            accountBankCardRepository.findById(loan.getRepayCardId()).ifPresent(c -> {
                c.setAgreeNo(repayAgreementNo);
                accountBankCardRepository.save(c);
            });
        }
        // 保存协议
        if (!CollectionUtils.isEmpty(applyVo.getFileInfoVoList())) {
            BankChannel bankChannel = loan.getChannel();
            String fileCreditId = loan.getCreditId();
            String fileRelateId = loan.getId();
            // 存在放款上传的协议
            List<FileInfoVo> fileInfoVoList = applyVo.getFileInfoVoList();
            fileInfoVoList.forEach(f -> {
                LoanFile loanFile = CreditLoanConvert.INSTANCE.toLoanFileEntity(f);
                loanFile.setCreditId(fileCreditId);
                loanFile.setRelatedId(fileRelateId);
                loanFile.setChannel(bankChannel);
                loanFile.setFileName(f.getFileType().getDesc());
                loanFile.setStage(LoanStage.LOAN.name());
                loanFile.setSignStatus(ProcessStatus.INIT);
                loanFileRepository.save(loanFile);
            });
        }
        return loan;
    }

    @Transactional
    public Credit creditApply(CreditApplyVo<ExtInfoVo> apply) {
        BankChannel bankChannel = apply.getBankChannel();

        UserInfoVo userInfoVo = apply.getUserInfo();
        // 身份证
        IdCardInfoVo idCardInfoVo = apply.getIdCardInfo();
        String certNo = idCardInfoVo.getCertNo();
        String birthStr = certNo.substring(BIRTH_POS_START, BIRTH_POS_END);
        String genderStr = certNo.substring(GENDER_POS_START, GENDER_POS_END);

        Account account = CreditLoanConvert.INSTANCE.toAccount(userInfoVo, idCardInfoVo);
        account.setBirthDay(LocalDate.parse(birthStr, DateTimeFormatter.BASIC_ISO_DATE));
        account.setGender(calcGender(genderStr));
        accountRepository.save(account);

        // 联系人
        String accountId = account.getId();
        List<UserContactInfoVo> userContactInfoVoList = apply.getUserContactInfoVoList();
        userContactInfoVoList.forEach(c -> {
            var contactInfo = CreditLoanConvert.INSTANCE.toUserContactInfo(c);
            contactInfo.setAccountId(accountId);
            accountContactInfoRepository.save(contactInfo);
        });

        // 设备信息
        AccountDevice device = new AccountDevice();
        device.setAccountId(accountId);
        if (StringUtil.isNotBlank(userInfoVo.getMac())) {
            device.setMac(userInfoVo.getMac());
        }
        if (StringUtil.isNotBlank(userInfoVo.getLatitude())) {
            device.setLatitude(userInfoVo.getLatitude());
            device.setLongitude(userInfoVo.getLongitude());
        }
        accountDeviceRepository.save(device);


        // credit
        Credit credit = CreditLoanConvert.INSTANCE.toCreditEntity(apply);
        credit.setAccountId(accountId);
        credit.setOuterCreditId(apply.getSysId());
        credit.setProductType(getRandomProductType());

        // shop
        MerchantInfoVo merchantInfoVo = apply.getMerchantInfo();
        if (merchantInfoVo != null) {
            Shop shop = CreditLoanConvert.INSTANCE.toMerchantShop(merchantInfoVo);
            shopRepository.save(shop);
            String shopId = shop.getId();
            credit.setShopId(shopId);
        }
        if (Objects.nonNull(apply.getExtInfo()) && Objects.nonNull(apply.getExtInfo().getCybkExtInfo())) {
            credit.setApplyChannel(apply.getExtInfo().getCybkExtInfo().getApplyChannel());
        }
        // bank_card
        BankCardInfoVo cardInfoVo = apply.getBankCardInfo();
        if (cardInfoVo != null) {
            AccountBankCard bankCard = CreditLoanConvert.INSTANCE.toBankCard(cardInfoVo, idCardInfoVo);
            bankCard.setChannel(bankChannel);
            accountBankCardRepository.save(bankCard);
            String cardId = bankCard.getId();
            credit.setCardId(cardId);
        }
        credit.setCreditStatus(CreditStatus.INIT);
        credit.setApplyTime(LocalDateTime.now());
        credit.setBankRate(bankChannel.getIrrRate());
        credit = creditRepository.save(credit);

        // 影像文件
        String creditId = credit.getId();
        List<FileInfoVo> fileInfoVoList = apply.getFileInfoVoList();
        fileInfoVoList.forEach(f -> {
            LoanFile loanFile = CreditLoanConvert.INSTANCE.toLoanFileEntity(f);
            loanFile.setCreditId(creditId);
            loanFile.setRelatedId(creditId);
            loanFile.setChannel(bankChannel);
            loanFile.setSignStatus(ProcessStatus.INIT);
            FileType fileType = f.getFileType();
            loanFile.setFileName(fileType.getDesc());
            loanFile.setStage(LoanStage.CREDIT.name());

            loanFileRepository.save(loanFile);
        });

        return credit;
    }

    private String getRandomProductType() {
        SecureRandom random = new SecureRandom();
        int num = random.nextInt(PRODUCT_TYPE_RANDMO);
        int idx = num < PRODUCT_TYPE_SPLIT_NUM ? 0 : 1;
        // 新客0， 老客1
        return PRODUCT_TYPE_ARR[idx];
    }


    public FailedCredit failedNotifyCreditApply(CreditApplyVo<ExtInfoVo> apply) {
        BankChannel bankChannel = apply.getBankChannel();

        UserInfoVo userInfoVo = apply.getUserInfo();
        // 身份证
        IdCardInfoVo idCardInfoVo = apply.getIdCardInfo();
        String certNo = idCardInfoVo.getCertNo();
        String birthStr = certNo.substring(BIRTH_POS_START, BIRTH_POS_END);
        String genderStr = certNo.substring(GENDER_POS_START, GENDER_POS_END);

        Account tmpAccount = CreditLoanConvert.INSTANCE.toAccount(userInfoVo, idCardInfoVo);
        tmpAccount.setBirthDay(LocalDate.parse(birthStr, DateTimeFormatter.BASIC_ISO_DATE));
        tmpAccount.setGender(calcGender(genderStr));
        FailedAccount account = CreditLoanConvert.INSTANCE.toFailedAccount(tmpAccount);
        failedAccountRepository.save(account);

        // 联系人
        String accountId = account.getId();
        List<UserContactInfoVo> userContactInfoVoList = apply.getUserContactInfoVoList();
        userContactInfoVoList.forEach(c -> {
            AccountContactInfo tmpContactInfo = CreditLoanConvert.INSTANCE.toUserContactInfo(c);
            FailedAccountContactInfo contactInfo = CreditLoanConvert.INSTANCE.toFailedAccountContactInfo(tmpContactInfo);
            contactInfo.setAccountId(accountId);
            failedAccountContactInfoRepository.save(contactInfo);
        });

        // 设备信息
        FailedAccountDevice device = new FailedAccountDevice();
        device.setAccountId(accountId);
        if (StringUtil.isNotBlank(userInfoVo.getMac())) {
            device.setMac(userInfoVo.getMac());
        }
        if (StringUtil.isNotBlank(userInfoVo.getLatitude())) {
            device.setLatitude(userInfoVo.getLatitude());
            device.setLongitude(userInfoVo.getLongitude());
        }
        failedAccountDeviceRepository.save(device);


        // credit
        Credit tmpCredit = CreditLoanConvert.INSTANCE.toCreditEntity(apply);
        FailedCredit credit = CreditLoanConvert.INSTANCE.toFailedCredit(tmpCredit);
        credit.setAccountId(accountId);
        credit.setOuterCreditId(apply.getSysId());
        credit.setProductType(getRandomProductType());


        // bank_card
        BankCardInfoVo cardInfoVo = apply.getBankCardInfo();
        if (cardInfoVo != null) {
            AccountBankCard tmpBankCard = CreditLoanConvert.INSTANCE.toBankCard(cardInfoVo, idCardInfoVo);
            FailedAccountBankCard bankCard = CreditLoanConvert.INSTANCE.toFailedBankCard(tmpBankCard);
            bankCard.setChannel(bankChannel);
            failedAccountBankCardRepository.save(bankCard);
            String cardId = bankCard.getId();
            credit.setCardId(cardId);
        }
        credit.setCreditStatus(CreditStatus.INIT);
        credit.setApplyTime(LocalDateTime.now());
        credit.setBankRate(bankChannel.getIrrRate());
        credit = failedCreditRepository.save(credit);

        // 影像文件
        String creditId = credit.getId();
        List<FileInfoVo> fileInfoVoList = apply.getFileInfoVoList();
        fileInfoVoList.forEach(f -> {
            LoanFile tmpLoanFile = CreditLoanConvert.INSTANCE.toLoanFileEntity(f);
            FailedLoanFile loanFile = CreditLoanConvert.INSTANCE.toFailedLoanFile(tmpLoanFile);
            loanFile.setCreditId(creditId);
            loanFile.setRelatedId(creditId);
            loanFile.setChannel(bankChannel);
            loanFile.setSignStatus(ProcessStatus.INIT);
            FileType fileType = f.getFileType();
            loanFile.setFileName(fileType.getDesc());
            loanFile.setStage(LoanStage.CREDIT.name());

            failedLoanFileRepository.save(loanFile);
        });


        return credit;
    }


    /**
     * 计算性别
     * <p></p>
     * 0 = 未知项
     * 1 = 男性
     * 2 = 女性
     * 3 = 女改男， 男性
     * 4 = 男改女， 女性
     * 9 = 未确定项（未指明）
     *
     * @param number
     * @return
     */
    private Gender calcGender(String number) {
        int gender = Integer.parseInt(number);
        if (gender % 2 == 0) {
            return Gender.FEMALE;
        }
        if (gender % 2 == 1) {
            return Gender.MALE;
        }
        return Gender.UNKNOWN;
    }

    public boolean signSuccess(Credit credit) {
        return signSuccess(credit.getChannel(), LoanStage.CREDIT, credit.getId(), credit.getAccountId());
    }

    public boolean signSuccess(Loan loan) {
        return signSuccess(loan.getChannel(), LoanStage.LOAN, loan.getId(), loan.getAccountId());
    }

    /**
     * 校验协议是否签署完成（模版签署）
     * 且：未开始签署的协议会发起签署申请
     * 要求：AgreementType 配置完整
     *
     * @param bankChannel 资方类型
     * @param loanStage   签署阶段
     * @param businessId  业务ID
     * @param accountId   用户ID
     * @return 已签署完成 true 未签署完成 false
     */
    private boolean signSuccess(BankChannel bankChannel, LoanStage loanStage, String businessId, String accountId) {
        boolean success = true;
        List<AgreementType> agrTypes = AgreementType.getAgreement(bankChannel, loanStage, SignatureType.TEMPLATE);
        for (AgreementType agrType : agrTypes) {
            List<LoanFile> loanFiles = loanFileRepository.findByRelatedIdAndFileType(businessId, agrType.getFileType());
            if (CollectionUtil.isNotEmpty(loanFiles)) {
                continue;
            }
            success = false;
            agreementSignature(businessId, accountId, agrType);
        }
        return success;
    }

    /**
     * 发起协议签署申请
     *
     * @param businessId 业务ID
     * @param accountId  用户ID
     * @param agrType    协议类型
     */
    private void agreementSignature(String businessId, String accountId, AgreementType agrType) {
        Account account = findAccountById(accountId);
        AgreementSignature agreementSignature = agreementSignatureRepository.findByBusinessIdAndChannelAndLoanStageAndFileType(
                businessId, agrType.getBankChannel(), agrType.getLoanStage(), agrType.getFileType());
        if (null == agreementSignature) {
            AgreementSignature saveAgreement = AgreementConvert.INSTANCE.initAgreementSignature(agrType, account, businessId);
            saveAgreement = agreementSignatureRepository.save(saveAgreement);
            //签章申请监听
            mqService.signatureApply(saveAgreement.getId());
        }
    }

    public LoanFileRepository getLoanFileRepository() {
        return loanFileRepository;
    }

    public AgreementSignatureRepository getAgreementSignatureRepository() {
        return agreementSignatureRepository;
    }

    public FailedLoanFileRepository getFailedLoanFileRepository() {
        return failedLoanFileRepository;
    }


    public LoanRepository getLoanRepository() {
        return loanRepository;
    }

    public CreditRepository getCreditRepository() {
        return creditRepository;
    }

    public ReconciliationFileRepository getRecFileRepository() {
        return recFileRepository;
    }

    public HXBKCreditFlowRepository getHXBKCreditFlowRepository() {
        return hxbkCreditFlowRepository;
    }

    public HXBKAccountAttrRepository getHXBKAccountAttrRepository() {
        return hxbkAccountAttrRepository;
    }

    public HXBKAntFileLogRepository getHXBKAntFileLogRepository() {
        return hxbkAntFileLogRepository;
    }


    public AccountBankCard bindApply(BindApplyVo apply, BankChannel channel) {
        AccountBankCard bankCard = CreditLoanConvert.INSTANCE.toBankCard(apply);
        bankCard.setChannel(channel);
        accountBankCardRepository.save(bankCard);
        return bankCard;
    }


    public List<BankRepayRecord> findSuccessClaimBankRepayRecord(BankChannel channel, LocalDate loanTime) {
        LocalDateTime startOfDay = loanTime.atStartOfDay();
        LocalDateTime nextDayStart = loanTime.plusDays(1L).atStartOfDay();
        return bankRepayRecordRepository.findListClaimSuccess(channel, startOfDay, nextDayStart);
    }

    public List<BankRepayRecord> findSuccessSettleBankRepayRecord(BankChannel channel, LocalDate loanTime) {
        LocalDateTime startOfDay = loanTime.atStartOfDay();
        LocalDateTime nextDayStart = loanTime.plusDays(1L).atStartOfDay();
        return bankRepayRecordRepository.findListSettleSuccessNotClaim(channel, startOfDay, nextDayStart);
    }


    public DefrayRecord findDefrayRecord(String defrayId) {
        return defrayRecordRepository.findById(defrayId).orElseThrow();
    }


    public Credit findCreditByAccountId(String accountId) {
        return creditRepository.findByAccountId(accountId);
    }

    public LoanFile findTrustSignFile(String relatedId) {
        return loanFileRepository.findFirstByRelatedIdOrderByCreatedTimeDesc(relatedId);
    }


    public List<BankRepayRecord> findBySysIdIn(List<String> sysIds) {
        return bankRepayRecordRepository.findBySysIdIn(sysIds);
    }

    /**
     * 判断是否存在正在借款的数据
     *
     * @param applyVo
     * @return
     */
    public boolean findChannelCreditCount(CreditApplyVo<ExtInfoVo> applyVo, BankChannel bankChannel) {
        List<CreditStatus> creditStatusList = List.of(CreditStatus.INIT, CreditStatus.PROCESSING);

        long credits = creditRepository.countByChannelAndCustCertNoAndCreditStatusIn(bankChannel, applyVo.getIdCardInfo().getCertNo(), creditStatusList);
        if (credits > 0) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否存在正在处理的授信
     *
     * @param applyVo
     * @return
     */
    public boolean findChannelLoanCount(CreditApplyVo<ExtInfoVo> applyVo, BankChannel bankChannel) {
        // 查询贷款记录，如果存在，则不允许重复申请（暂时注释掉，后续再做处理
        List<LoanStatus> loanStatusList = List.of(LoanStatus.INIT, LoanStatus.PROCESSING);
        long loanStatusIn = loanRepository.countByChannelAndCustCertNoAndLoanStatusIn(bankChannel, applyVo.getIdCardInfo().getCertNo(), loanStatusList);
        if (loanStatusIn > 0) {
            return false;
        }
        return true;
    }
}
