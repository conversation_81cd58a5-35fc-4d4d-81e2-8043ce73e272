package com.jinghang.capital.core.entity;


import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.BankChannelType;
import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.CreditType;
import com.jinghang.capital.core.enums.FlowChannel;
import com.jinghang.capital.core.vo.ProductVo;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 授信表
 */
@Entity
@Table(name = "credit")
public class Credit extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 5697556825676213023L;

    /**
     * 用户id
     */

    private String accountId;
    /**
     * 门店id
     */

    private String shopId;
    /**
     * 外部id
     */

    private String outerCreditId;
    /**
     * 直接资金方（对接方）
     */
    private String creditNo;
    /**
     * 背后资金方（银行）
     */
    private String creditContractNo;

    /**
     * 资产端授信申请合同编号
     */
    private String creditApplyContractNo;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;
    /**
     * 申请授信金额
     */
    private BigDecimal creditAmt;

    /**
     * 资方授信成功金额
     */
    private BigDecimal creditResultAmt;

    /**
     * 借款金额
     */
    private BigDecimal loanAmt;

    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;

    /**
     * 辅助模式
     */
    private String assistMode;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 通过时间
     */
    private LocalDateTime passTime;

    /**
     * 资方有效期
     */
    private LocalDateTime capExpireTime;
    /**
     * 申请期数
     */

    private Integer periods;
    /**
     * 产品编号
     */
    @Enumerated(EnumType.STRING)
    private ProductVo productNo;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 申请渠道(长银)
     */
    private String applyChannel;

    /**
     * 卡号
     */

    private String cardId;
    /**
     * 授信状态
     */
    @Enumerated(EnumType.STRING)
    private CreditStatus creditStatus;

    /**
     * 用户姓名
     */
    private String custName;
    /**
     * 用户手机
     */
    private String custMobile;
    /**
     * 用户身份证
     */
    private String custCertNo;
    /**
     * 对客费率
     */
    private BigDecimal customRate;
    /**
     * 对资费率
     */
    private BigDecimal bankRate;

    /**
     * 产品类型
     */
    private String productType;

    /**
     *
     */
    @Enumerated(EnumType.STRING)
    private BankChannelType channelType;

    public CreditType getCreditType() {
        return creditType;
    }

    public void setCreditType(CreditType creditType) {
        this.creditType = creditType;
    }

    /**
     * 授信类型，调额授信，正常授信
     */
    @Enumerated(EnumType.STRING)
    private CreditType creditType;

    public String getCreditNo() {
        return creditNo;
    }


    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getCreditContractNo() {
        return creditContractNo;
    }

    public void setCreditContractNo(String creditContractNo) {
        this.creditContractNo = creditContractNo;
    }

    /**
     * 用户id
     */
    public String getAccountId() {
        return this.accountId;
    }

    /**
     * 用户id
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 门店id
     */
    public String getShopId() {
        return this.shopId;
    }

    /**
     * 门店id
     */
    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    /**
     * 外部id
     */
    public String getOuterCreditId() {
        return this.outerCreditId;
    }

    /**
     * 外部id
     */
    public void setOuterCreditId(String outerCreditId) {
        this.outerCreditId = outerCreditId;
    }

    public String getCreditApplyContractNo() {
        return creditApplyContractNo;
    }

    public void setCreditApplyContractNo(String creditApplyContractNo) {
        this.creditApplyContractNo = creditApplyContractNo;
    }

    /**
     * 资方渠道
     */
    public BankChannel getChannel() {
        return this.channel;
    }

    /**
     * 资方渠道
     */
    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getAssistMode() {
        return assistMode;
    }

    public void setAssistMode(String assistMode) {
        this.assistMode = assistMode;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public BigDecimal getCreditResultAmt() {
        return creditResultAmt;
    }

    public void setCreditResultAmt(BigDecimal creditResultAmt) {
        this.creditResultAmt = creditResultAmt;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    /**
     * 申请时间
     */
    public LocalDateTime getApplyTime() {
        return this.applyTime;
    }

    /**
     * 申请时间
     */
    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    /**
     * 通过时间
     */
    public LocalDateTime getPassTime() {
        return this.passTime;
    }

    /**
     * 通过时间
     */
    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    /**
     * 资方有效期
     */
    public LocalDateTime getCapExpireTime() {
        return this.capExpireTime;
    }

    /**
     * 资方有效期
     */
    public void setCapExpireTime(LocalDateTime capExpireTime) {
        this.capExpireTime = capExpireTime;
    }

    /**
     * 申请期数
     */
    public Integer getPeriods() {
        return this.periods;
    }

    /**
     * 申请期数
     */
    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    /**
     * 产品编号
     */
    public ProductVo getProductNo() {
        return this.productNo;
    }

    /**
     * 产品编号
     */
    public void setProductNo(ProductVo productNo) {
        this.productNo = productNo;
    }

    /**
     * 卡号
     */
    public String getCardId() {
        return this.cardId;
    }

    /**
     * 卡号
     */
    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    /**
     * 授信状态
     */
    public CreditStatus getCreditStatus() {
        return this.creditStatus;
    }

    /**
     * 授信状态
     */
    public void setCreditStatus(CreditStatus creditStatus) {
        this.creditStatus = creditStatus;
    }

    /**
     * 用户姓名
     */
    public String getCustName() {
        return this.custName;
    }

    /**
     * 用户姓名
     */
    public void setCustName(String custName) {
        this.custName = custName;
    }

    /**
     * 用户手机
     */
    public String getCustMobile() {
        return this.custMobile;
    }

    /**
     * 用户手机
     */
    public void setCustMobile(String custMobile) {
        this.custMobile = custMobile;
    }

    /**
     * 用户身份证
     */
    public String getCustCertNo() {
        return this.custCertNo;
    }

    /**
     * 用户身份证
     */
    public void setCustCertNo(String custCertNo) {
        this.custCertNo = custCertNo;
    }

    /**
     * 对客费率
     */
    public BigDecimal getCustomRate() {
        return this.customRate;
    }

    /**
     * 对客费率
     */
    public void setCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
    }

    /**
     * 对资费率
     */
    public BigDecimal getBankRate() {
        return this.bankRate;
    }

    /**
     * 对资费率
     */
    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BankChannelType getChannelType() {
        return channelType;
    }

    public void setChannelType(BankChannelType channelType) {
        this.channelType = channelType;
    }

    @Override
    public String prefix() {
        return "CR";
    }
}
