package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.service.ChannelSupport;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;

/**
 * 合同信息相关服务
 */
public interface BankContractInfoService extends ChannelSupport {
    SignApplyReq fetchDynamicSignParam(String businessId, AgreementType agreementType);

    SignApplyReq fetchTemplateSignParam(String businessId, AgreementType agreementType);

}
